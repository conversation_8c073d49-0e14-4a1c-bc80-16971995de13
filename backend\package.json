{"name": "smart-task-manager-backend", "version": "1.0.0", "description": "Backend API for Smart Task Manager with AI-powered prioritization", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "migrate": "node src/database/migrate.js", "seed": "node src/database/seed.js"}, "keywords": ["task-management", "ai", "productivity", "api", "express"], "author": "", "license": "MIT", "dependencies": {"express": "^4.18.2", "cors": "^2.8.5", "helmet": "^7.1.0", "morgan": "^1.10.0", "dotenv": "^16.3.1", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "pg": "^8.11.3", "pg-hstore": "^2.3.4", "sequelize": "^6.35.0", "joi": "^17.11.0", "socket.io": "^4.7.4", "node-cron": "^3.0.3", "axios": "^1.6.2", "multer": "^1.4.5-lts.1", "compression": "^1.7.4", "express-rate-limit": "^7.1.5", "winston": "^3.11.0", "@tensorflow/tfjs-node": "^4.15.0", "natural": "^6.8.0", "moment-timezone": "^0.5.43"}, "devDependencies": {"nodemon": "^3.0.2", "jest": "^29.7.0", "supertest": "^6.3.3", "@types/jest": "^29.5.8", "eslint": "^8.54.0", "prettier": "^3.1.0"}, "engines": {"node": ">=18.0.0"}}